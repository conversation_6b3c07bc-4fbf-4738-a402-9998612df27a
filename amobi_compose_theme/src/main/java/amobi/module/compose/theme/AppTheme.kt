package amobi.module.compose.theme

import android.annotation.SuppressLint
import android.app.Activity
import android.os.Build
import android.view.View
import android.view.WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS
import android.view.WindowManager
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.platform.LocalView
import androidx.core.content.ContextCompat
import androidx.core.view.WindowCompat


@SuppressLint("CompositionLocalNaming")
val AppColors = compositionLocalOf {
    AppColorsPalette()
}

@Immutable
data class AppColorsPalette(
    val primary: Color = Color.Unspecified,
    val ripple: Color = Color.Unspecified,
    val text: Color = Color.Unspecified,
    val onText: Color = Color.Unspecified,
    val titleText: Color = Color.Unspecified,
    val progressIndicator: Color = Color.Unspecified,
    val progressIndicatorTrack: Color = Color.Unspecified,
    val buttonText: Color = Color.Unspecified,
    val buttonColor: Color = Color.Unspecified,
    val homeBottomNavBar: Color = Color.Unspecified,
    val homeBottomNavBarActiveTint: Color = Color.Unspecified,
    val homeBottomNavBarInactiveTint: Color = Color.Unspecified,
    val settingItemText: Color = Color.Unspecified,
    val settingItemTextHint: Color = Color.Unspecified,
    val settingItemDivider: Color = Color.Unspecified,
    val bottomSheetBackground: Color = Color.Unspecified,
    val radioButtonActive: Color = Color.Unspecified,
    val radioButtonInactive: Color = Color.Unspecified,
    val textContentHistory: Color = Color.Unspecified,
    val backgroundContentChatBox: Color = Color.Unspecified,
    val backgroundContentHistory: Color = Color.Unspecified,
    val backgroundEditText: Color = Color.Unspecified,
    val backgroundLeftChat: Color = Color.Unspecified,
    val backgroundRightChat: Color = Color.Unspecified,
    val backgroundSearchBarColors: Color = Color.Unspecified,
    val textHintColor: Color = Color.Unspecified,
    val selectModelBackground: Color = Color.Unspecified,
    val aiChatModelBackground: Color = Color.Unspecified,
    val userChatModelBackground: Color = Color.Unspecified,

    val actionBarColor: Color = Color.Unspecified,
    val buttonActive: Color = Color.Unspecified,
    val buttonInactive: Color = Color.Unspecified,
    val buttonShadow: Color = Color.Unspecified,
    val buttonInactiveText: Color = Color.Unspecified,
    val switchThumbActive: Color = Color.Unspecified,
    val switchThumbInactive: Color = Color.Unspecified,
    val switchTrackActive: Color = Color.Unspecified,
    val switchTrackInactive: Color = Color.Unspecified,
    val switchBorderActive: Color = Color.Unspecified,
    val switchBorderInactive: Color = Color.Unspecified,
    val divider1: Color = Color.Unspecified,
    val divider2: Color = Color.Unspecified,
    val divider3: Color = Color.Unspecified,
    val iconColor: Color = Color.Unspecified,
    val getCoinColor: Color = Color.Unspecified,

    // new
    val backgroundContent: Color = Color.Unspecified,
    val titleContent: Color = Color.Unspecified,
    val textError: Color = Color.Unspecified,
    val backgroundGetCredits: Color = Color.Unspecified,
    val textHintColorItemGetCredits: Color = Color.Unspecified,
    val borderColorButtonGetCredits: Color = Color.Unspecified,
    val moneyTextColor: Color = Color.Unspecified,
    val checkColor: Color = Color.Unspecified,
    val unCheckColor: Color = Color.Unspecified,
    val bottomSheetContentBackground: Color = Color.Unspecified,
    val bottomSheetContentTitle: Color = Color.Unspecified,
    val bottomSheetContentHint: Color = Color.Unspecified,
    val borderSearch: Color = Color.Unspecified,
    val sendBarBackground: Color = Color.Unspecified,
    val textHintInput: Color = Color.Unspecified,
    val textHintItemCoinHistory: Color = Color.Unspecified,
    val border: Color = Color.Unspecified,
    val tooltipBackground: Color = Color.Unspecified,
    val backgroundInputChatBotCompose: Color = Color.Unspecified,
    val borderColorThemeButton: Color = Color.Unspecified,
    val focusedContainerColor: Color = Color.Unspecified,
    val unfocusedContainerColor: Color = Color.Unspecified,
    val backgroundDropdownMenu: Color = Color.Unspecified,
    val deleteColor: Color = Color.Unspecified,
    val isFavoriteColor: Color = Color.Unspecified,
    val titleHintText: Color = Color.Unspecified,
    val itemHistoryHintText: Color = Color.Unspecified,
    val dialogBackground: Color = Color.Unspecified,
    val textLeftChat: Color = Color.Unspecified,
    val textRightChat: Color = Color.Unspecified,
    val imagePicker: Color = Color.Unspecified,
    val selectThemeButtonBackground: Color = Color.Unspecified,
    val unSelectThemeButtonBackground: Color = Color.Unspecified,
    val selectThemeButtonText: Color = Color.Unspecified,
    val unSelectThemeButtonText: Color = Color.Unspecified,
    val selectSortOption: Color = Color.Unspecified,
    val buttonActiveGetCredits: Color = Color.Unspecified,
    val buttonInactiveGetCredits: Color = Color.Unspecified,
    val backgroundButtonPrice: Color = Color.Unspecified,

    )


val darkColorScheme = darkColorScheme(
    primary = Color(0xFF0ABAB5),
    secondary = Color(0xFF0ABAB5),
    background = Color(0xFF000B1D),
    onBackground = Color(0xffFDFDFF),
    tertiary = Color(0xff9CCAFC),
    surface = Color(0xff003166),
    error = Color(0xffDB1C02),
    onSurface = Color.White,
    onSurfaceVariant = Color.White
)

val appCustomColorDark = AppColorsPalette(
    primary = Color(0xff0F2F5D),
    ripple = Color(0xff0F2F5D),
    text = Color(0xFFFFFFFF),
    onText = Color(0xFFFFFFFF),
    titleText = Color(0xFFFFFFFF),
    progressIndicator = Color(0xFFFFFFFF),
    progressIndicatorTrack = Color(0x00FFFFFF),
    buttonText = Color.White,
    homeBottomNavBar = Color(0xFF000B1D),
    homeBottomNavBarActiveTint = Color(0xFF0ABAB5),
    homeBottomNavBarInactiveTint = Color(0xFFC0C5CA),
    settingItemText = Color(0xffE7E8EA),
    settingItemTextHint = Color(0xffE7E8EA),
    settingItemDivider = Color(0x3345484B),
    buttonColor = Color(0xFF0ABAB5),
    bottomSheetBackground = Color(0xFF000B1D),
    radioButtonActive = Color(0xFF0ABAB5),
    radioButtonInactive = Color(0xFFC0C5CA),
    selectModelBackground = Color(0xffB9BCC0),
    textContentHistory = Color(0xffE7E8EA),
    backgroundContentChatBox = Color(0xff45484B),
    backgroundContentHistory = Color(0xff45484B),
    backgroundLeftChat = Color(0xFF132537),
    backgroundRightChat = Color(0xFFE0F7FA),
    backgroundEditText = Color(0xFF000B1D),
    backgroundSearchBarColors = Color(0xff45484B),
    textHintColor = Color(0xFF566573),
    aiChatModelBackground = Color(0xff2C81FF),
    userChatModelBackground = Color(0xff8B9097),

    actionBarColor = Color.Transparent,
    buttonActive = Color(0xFF0ABAB5),
    buttonInactive = Color(0x332C73ED),
    buttonShadow = Color(0x26010C19),
    buttonInactiveText = Color(0x7FF1F8FF),
    switchThumbActive = Color(0xFFFFFFFF),
    switchThumbInactive = Color(0xFF9A9A9A),
    switchTrackActive = Color(0xFF087CF7),
    switchTrackInactive = Color(0xFFDADADA),
    switchBorderActive = Color(0xFF087CF7),
    switchBorderInactive = Color(0xFF9A9A9A),
    divider1 = Color(0xFF2C3E50),
    divider2 = Color(0xFF2C3E50),
    divider3 = Color(0xFFD5D8DC),

    getCoinColor = Color(0xFFE6C083),

    //new
    backgroundContent = Color(0xFF132537),
    titleContent = Color(0xFFD5D8DC),
    textError = Color(0xFFF64C4C),
    backgroundGetCredits = Color(0xFF040809),
    textHintColorItemGetCredits = Color(0xFFD5D8DC),
    borderColorButtonGetCredits = Color(0x33FFFFFF),
    moneyTextColor = Color(0xFF000B1D),
    checkColor = Color(0xFF0ABAB5),
    unCheckColor = Color(0xFFC0C5CA),
    bottomSheetContentBackground = Color(0xFF132537),
    bottomSheetContentTitle = Color(0xFFFFFFFF),
    bottomSheetContentHint = Color(0xFF959EA7),
    borderSearch = Color(0xFF2C3E50),
    sendBarBackground = Color(0xFF132537),
    textHintInput = Color(0xFFC0C5CA),
    textHintItemCoinHistory = Color(0xFF566573),
    border = Color(0xFFC0C5CA),
    tooltipBackground = Color(0xFF132537),
    backgroundInputChatBotCompose = Color(0xFF132537),
    borderColorThemeButton = Color(0xFF132537),
    focusedContainerColor = Color(0xFF2C3E50),
    unfocusedContainerColor = Color(0xFF2C3E50),
    backgroundDropdownMenu = Color(0xFF566573),
    deleteColor = Color(0xFFF64C4C),
    isFavoriteColor = Color(0xFFF64C4C),
    titleHintText = Color(0xFF959EA7),
    itemHistoryHintText = Color(0xFFC0C5CA),
    dialogBackground = Color(0xFF132537),
    textLeftChat = Color(0xFFFFFFFF),
    textRightChat = Color(0xFF132537),
    imagePicker = Color(0xFFD5D8DC),
    selectThemeButtonBackground = Color(0xFF566573),
    unSelectThemeButtonBackground = Color(0xFF132537),
    selectThemeButtonText = Color(0xFFFFFFFF),
    unSelectThemeButtonText = Color(0xFF566573),
    selectSortOption = Color(0xFF959EA7),
    buttonActiveGetCredits = Color(0xB2FFFFFF),
    buttonInactiveGetCredits = Color(0x33FFFFFF),
    backgroundButtonPrice = Color(0xFFE0F7FA),
)

val lightColorScheme = lightColorScheme(
    primary = Color(0xFF0ABAB5),
    secondary = Color(0xFF0ABAB5),
    background = Color(0xFFFFFFFF),
    onBackground = Color(0xff2A2A2A),
    tertiary = Color(0xff0056b3),
    surface = Color(0xff003166),
    error = Color(0xffDB1C02),
    onSurface = Color.White,
    onSurfaceVariant = Color.White
)

val appCustomColorLight = AppColorsPalette(
    primary = Color(0xff0F2F5D),
    ripple = Color(0xff0F2F5D),
    text = Color(0xFF2C3E50),
    onText = Color(0xFFFFFFFF),
    titleText = Color(0xFF132537),
    progressIndicator = Color(0xFFFFFFFF),
    progressIndicatorTrack = Color(0x00FFFFFF),
    buttonText = Color.White,
    homeBottomNavBar = Color(0xFFFFFFFF),
    homeBottomNavBarActiveTint = Color(0xFF0ABAB5),
    homeBottomNavBarInactiveTint = Color(0xFFC0C5CA),
    settingItemText = Color(0xff45484B),
    settingItemTextHint = Color(0xff45484B),
    settingItemDivider = Color(0x3345484B),
    buttonColor = Color(0xFF0ABAB5),
    bottomSheetBackground = Color(0xFFFFFFFF),
    radioButtonActive = Color(0xFF0ABAB5),
    radioButtonInactive = Color(0xFFC0C5CA),
    selectModelBackground = Color(0xffE9E9E9),
    textContentHistory = Color(0xff45484B),
    backgroundContentChatBox = Color(0xffE7E8EA),
    backgroundContentHistory = Color.White,
    backgroundLeftChat = Color(0xFFF8F9FA),
    backgroundRightChat = Color(0xFFE0F7FA),
    backgroundEditText = Color(0xFFFFFFFF),
    backgroundSearchBarColors = Color(0xffE7E8EA),
    textHintColor = Color(0xFF959EA7),
    aiChatModelBackground = Color(0xffA8C8F6),
    userChatModelBackground = Color(0xffB9BCC0),

    actionBarColor = Color.Transparent,
    buttonActive = Color(0xFF0ABAB5),
    buttonInactive = Color(0x332C73ED),
    buttonShadow = Color(0x26010C19),
    buttonInactiveText = Color(0x7FF1F8FF),
    switchThumbActive = Color(0xFFFFFFFF),
    switchThumbInactive = Color(0xFF9A9A9A),
    switchTrackActive = Color(0xFF087CF7),
    switchTrackInactive = Color(0xFFDADADA),
    switchBorderActive = Color(0xFF087CF7),
    switchBorderInactive = Color(0xFF9A9A9A),
    divider1 = Color(0xFFECEBEE),
    divider2 = Color(0xFFEAECED),
    divider3 = Color(0xFFD5D8DC),

    getCoinColor = Color(0xFFE6C083),

    //new
    backgroundContent = Color(0xFFF8F9FA),
    titleContent = Color(0xFF566573),
    textError = Color(0xFFF64C4C),
    backgroundGetCredits = Color(0xFF040809),
    textHintColorItemGetCredits = Color(0xFFD5D8DC),
    borderColorButtonGetCredits = Color(0x33FFFFFF),
    moneyTextColor = Color(0xFF000B1D),
    checkColor = Color(0xFF0ABAB5),
    unCheckColor = Color(0xFFC0C5CA),
    bottomSheetContentBackground = Color(0xFFFAFAFA),
    bottomSheetContentTitle = Color(0xFF2C3159),
    bottomSheetContentHint = Color(0xFF5D6186),
    borderSearch = Color(0xFFD5D8DC),
    sendBarBackground = Color(0xFFE0F7FA),
    textHintInput = Color(0xFFC0C5CA),
    textHintItemCoinHistory = Color(0xFF566573),
    border = Color(0xFFC0C5CA),
    tooltipBackground = Color(0xFFE0F7FA),
    backgroundInputChatBotCompose = Color(0xFFF4F4F4),
    borderColorThemeButton = Color(0xFFF4F4F4),
    focusedContainerColor = Color(0xFFF4F4F4),
    unfocusedContainerColor = Color(0xFFF4F4F4),
    backgroundDropdownMenu = Color(0xFFF4F4F4),
    deleteColor = Color(0xFFF64C4C),
    isFavoriteColor = Color(0xFFF64C4C),
    titleHintText = Color(0xFF2C3E50),
    itemHistoryHintText = Color(0xFF132537),
    dialogBackground = Color(0xFFFFFFFF),
    textLeftChat = Color(0xFF132537),
    textRightChat = Color(0xFF132537),
    imagePicker = Color(0xFF959EA7),
    selectThemeButtonBackground = Color(0xFFF4F4F4),
    unSelectThemeButtonBackground = Color(0xFFFFFFFF),
    selectThemeButtonText = Color(0xFF000B1D),
    unSelectThemeButtonText = Color(0xFF000B1D),
    selectSortOption = Color(0xFFF4F4F4),
    buttonActiveGetCredits = Color(0xB2FFFFFF),
    buttonInactiveGetCredits = Color(0x33FFFFFF),
    backgroundButtonPrice = Color(0xFFE0F7FA),

    )


@Composable
fun AppThemeWrapper(
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit,
) {
    val context = LocalView.current.context
    SideEffect {
        val window = (context as? Activity)?.window ?: return@SideEffect
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        WindowCompat.getInsetsController(window, window.decorView).apply {
            isAppearanceLightStatusBars = !darkTheme
        }
    }

    CompositionLocalProvider(
        AppColors provides
                if (darkTheme)
                    appCustomColorDark
                else
                    appCustomColorLight
    ) {
        MaterialTheme(
            colorScheme = if (darkTheme)
                darkColorScheme
            else
                lightColorScheme,
        ) {
            content()
        }
    }
}