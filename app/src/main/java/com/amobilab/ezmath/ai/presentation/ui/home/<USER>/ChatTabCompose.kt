package com.amobilab.ezmath.ai.presentation.ui.home.tabs

import amobi.module.common.configs.PrefAssist
import amobi.module.common.utils.debugLog
import amobi.module.compose.extentions.AppPreview
import amobi.module.compose.extentions.PreviewAssist
import amobi.module.compose.foundation.AppBox
import amobi.module.compose.foundation.AppColumn
import amobi.module.compose.foundation.AppGlideImage
import amobi.module.compose.foundation.AppRow
import amobi.module.compose.foundation.AppSpacer
import amobi.module.compose.foundation.AppText
import amobi.module.compose.theme.AppColors
import amobi.module.compose.theme.AppFontSize
import amobi.module.compose.theme.AppThemeWrapper
import android.annotation.SuppressLint
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.isImeVisible
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.amobilab.ezmath.ai.R
import com.amobilab.ezmath.ai.data.pref.PrefConst
import com.amobilab.ezmath.ai.presentation.common.shared_components.BottomBarFreeTrialInfo
import com.amobilab.ezmath.ai.presentation.common.shared_components.BottomBarInput
import com.amobilab.ezmath.ai.presentation.common.shared_components.HorizontalScrollFeatureCard
import com.amobilab.ezmath.ai.presentation.common.shared_values.ChatQuestionMode
import com.amobilab.ezmath.ai.presentation.common.shared_viewmodels.MainDataViewModel
import com.amobilab.ezmath.ai.utils.BitmapUtils
import java.util.Locale
import kotlin.math.roundToLong

@AppPreview
@Composable
fun ChatTabComposePreview() {
    PreviewAssist.initVariables(LocalContext.current)
    AppThemeWrapper {
        ChatTabCompose(
            innerPaddingHome = PaddingValues(),
            nextMode = {},
            onSend = { _, _, _ -> }
        )
    }
}

@OptIn(ExperimentalLayoutApi::class)
@SuppressLint("CoroutineCreationDuringComposition")
@Composable
fun ChatTabCompose(
    innerPaddingHome: PaddingValues,
    nextMode: (ChatQuestionMode) -> Unit,
    onSend: (mode: ChatQuestionMode, prompt: String, imageUri: String) -> Unit,
) {

    val mainDataViewModel = hiltViewModel<MainDataViewModel>()

    val context = LocalContext.current

    val showLoading = remember { mutableStateOf(false) }

    val chatModeSelected = remember { mutableStateOf(false) }

    DisposableEffect(Unit) {
        // Tác vụ khi Composable được tạo
        onDispose {
            // Lưu thông tin trước khi Composable bị hủy
            debugLog("ChatTabCompose: onDispose")
            mainDataViewModel.stopListening()
        }
    }
    // Tự động cuộn đến cuối khi danh sách chat thay đổi
    val scrollState = rememberScrollState()
    LaunchedEffect(scrollState.maxValue) {
        scrollState.animateScrollTo(
            value = scrollState.maxValue,
            animationSpec = tween(500)
        )
    }
    val isImeVisible = WindowInsets.isImeVisible
    val isImeVisible2 = WindowInsets.ime.asPaddingValues()
    val contentPadding =
        if (isImeVisible) {
            if (isImeVisible2.calculateBottomPadding() < innerPaddingHome.calculateBottomPadding()) {
                0.dp
            } else {
                0.dp
            }
        } else innerPaddingHome.calculateBottomPadding()

    //====================================================================================================

    Scaffold(
        modifier = Modifier.padding(bottom = contentPadding)
    ) { innerPadding ->
        AppColumn(
            Modifier
                .fillMaxSize()
                .padding(top = innerPadding.calculateTopPadding())
                .padding(top = 12.dp)
                .background(MaterialTheme.colorScheme.background),
        ) {
            AppRow(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                AppColumn(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    AppRow(
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        AppText(
                            text = stringResource(R.string.hello),
                            fontSize = AppFontSize.PLUS_SIZE,
                            fontWeight = FontWeight.W700,
                            lineHeight = 32.sp,
                            color = AppColors.current.text
                        )
                        AppSpacer(4.dp)
                        AppGlideImage(
                            resId = R.drawable.ic_cloud_1,
                            modifier = Modifier.size(28.dp)
                        )
                    }
                    AppText(
                        text = stringResource(R.string.how_can_i_assist_you_today),
                        fontSize = AppFontSize.BODY1,
                        fontWeight = FontWeight.W400,
                        lineHeight = 24.sp,
                        color = AppColors.current.titleContent,
                        maxLines = 2,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                AppSpacer(modifier = Modifier.width(12.dp)) // spacing between text and coin

                // Right: Coin block
                AppRow(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    AppGlideImage(
                        resId = R.drawable.svg_ic_coin_2,
                        modifier = Modifier.size(20.dp)
                    )
                    AppSpacer(4.dp)
                    val coinTotal = PrefAssist.getLong(PrefConst.TOTAL_COIN_BALANCE)
                    AppText(
                        text =
                            if (coinTotal > 1000000)
                                "${
                                    String.format(
                                        Locale.getDefault(),
                                        "%,d",
                                        (coinTotal.toDouble() / 1000000).roundToLong()
                                    )
                                }M"
                            else if (coinTotal > 10000)
                                "${
                                    String.format(
                                        Locale.getDefault(),
                                        "%,d",
                                        (coinTotal.toDouble() / 1000).roundToLong()
                                    )
                                }K"
                            else String.format(Locale.getDefault(), "%,d", coinTotal),
                        fontSize = AppFontSize.BODY2,
                        fontWeight = FontWeight.W700,
                        lineHeight = 20.sp,
                        maxLines = 1,
                        color = AppColors.current.text
                    )
                }
            }

            AppSpacer(12.dp)

            AppColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .weight(1f)
                    .padding(horizontal = 16.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                HorizontalScrollFeatureCard()
                AppSpacer(12.dp)
                AppText(
                    modifier = Modifier
                        .padding(start = 12.dp),
                    text = stringResource(R.string.other_subjects).uppercase(),
                    fontSize = AppFontSize.BODY2,
                    fontWeight = FontWeight.W400,
                    lineHeight = 20.sp,
                    color = AppColors.current.titleContent
                )
                if (chatModeSelected.value)
                    AppBox(modifier = Modifier.weight(1f)) {}
                else
                    ChatTabDefault(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        onSelectChatMode = {
//                                viewModel.modeScan.value = it
//                                chatModeSelected.value = true
                            nextMode(it)
                        }
                    )
            }
            // Bottom components with IME awareness
            AppColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .imePadding() // Apply imePadding here at the bottom container level
            ) {
                BottomBarFreeTrialInfo()
                BottomBarInput(
                    isLoadingData = showLoading.value,
                    onSend = { prompt, imageUri, _ ->
                        val urlBitmap =
                            BitmapUtils.getBitmapFromUriOrFile(context.contentResolver, imageUri)
                                ?.let { mainDataViewModel.saveImageToCache(context, it) }
                        onSend(ChatQuestionMode.Default, prompt, urlBitmap ?: "")
                    }
                )
            }
        }
    }
}


@Composable
fun ChatTabDefaultItem(
    modifier: Modifier = Modifier,
    chatQuestionMode: ChatQuestionMode,
    onClick: () -> Unit = {},
) {
    AppRow(
        modifier = modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(12.dp))
            .background(AppColors.current.backgroundContent)
            .clickable {
                onClick()
            }
            .padding(start = 16.dp, end = 12.dp, top = 16.dp, bottom = 16.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        // Icon and Text
        AppRow(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Icon with gradient background
            AppGlideImage(
                modifier = Modifier
                    .size(40.dp)
                    .background(color = Color.White, shape = RoundedCornerShape(8.dp))
                    .padding(8.dp),
                resId = chatQuestionMode.iconId,
            )

            // Title and subtitle
            AppColumn {
                AppText(
                    text = stringResource(chatQuestionMode.titleId),
                    color = AppColors.current.text,
                    fontSize = AppFontSize.BODY1,
                    fontWeight = FontWeight.W700,
                    lineHeight = 24.sp
                )

                AppText(
                    text = stringResource(chatQuestionMode.subtitleId),
                    color = AppColors.current.titleContent,
                    fontSize = AppFontSize.BODY2,
                    fontWeight = FontWeight.W400,
                    lineHeight = 20.sp,
                )
            }
        }

        // Radio button
        AppGlideImage(
            modifier = Modifier
                .size(24.dp),
            resId = R.drawable.svg_ic_left_arrow_new,
        )
    }
}


@Composable
fun ChatTabDefault(
    modifier: Modifier = Modifier,
    onSelectChatMode: (ChatQuestionMode) -> Unit = {}
) {
    AppColumn(
        modifier = modifier
    ) {
        ChatTabDefaultItem(
            chatQuestionMode = ChatQuestionMode.Math,
        ){
            onSelectChatMode(ChatQuestionMode.Math)
        }
        AppSpacer(8.dp)
        ChatTabDefaultItem(
            chatQuestionMode = ChatQuestionMode.Translate,
        ){
            onSelectChatMode(ChatQuestionMode.Translate)
        }
        AppSpacer(8.dp)
        ChatTabDefaultItem(
            chatQuestionMode = ChatQuestionMode.Writing,
        ){
            onSelectChatMode(ChatQuestionMode.Writing)
        }
        AppSpacer(8.dp)
        ChatTabDefaultItem(
            chatQuestionMode = ChatQuestionMode.Geography,
        ){
            onSelectChatMode(ChatQuestionMode.Geography)
        }
        AppSpacer(8.dp)
        ChatTabDefaultItem(
            chatQuestionMode = ChatQuestionMode.Chemistry,
        ){
            onSelectChatMode(ChatQuestionMode.Chemistry)
        }
        //
    }
}
